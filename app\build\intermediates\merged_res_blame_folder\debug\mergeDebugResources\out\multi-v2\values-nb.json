{"logs": [{"outputFile": "com.opms.app-mergeDebugResources-69:/values-nb/values-nb.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.11.1\\transforms\\f5d6d0c532ad067d6a1644a9775866cb\\transformed\\core-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,125", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3348,3442,3544,3641,3740,3848,3954,10443", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3437,3539,3636,3735,3843,3949,4069,10539"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.11.1\\transforms\\2cd0a0e4f151fb72b6685fb11f793153\\transformed\\jetified-ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "48,49,50,51,52,56,57,116,117,118,119,121,122,124,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4374,4467,4548,4645,4745,5057,5133,9754,9843,9925,9989,10131,10211,10373,10544,10621,10688", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "4462,4543,4640,4740,4828,5128,5216,9838,9920,9984,10048,10206,10288,10438,10616,10683,10803"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.11.1\\transforms\\40058f5d8b846502ed4c9a4f2dd09bdb\\transformed\\appcompat-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,10293", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,10368"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.11.1\\transforms\\9943be27f2bfc483346a4a67c97b0210\\transformed\\navigation-ui-2.7.7\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,113", "endOffsets": "156,270"}, "to": {"startLines": "114,115", "startColumns": "4,4", "startOffsets": "9534,9640", "endColumns": "105,113", "endOffsets": "9635,9749"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.11.1\\transforms\\004afb5fa568b2f9d4cdb73bc1cfe8ba\\transformed\\material-1.11.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1030,1122,1190,1250,1337,1401,1463,1527,1595,1660,1714,1823,1881,1943,1997,2072,2192,2274,2354,2488,2566,2646,2769,2857,2935,2989,3040,3106,3174,3248,3338,3414,3485,3563,3633,3703,3803,3892,3970,4058,4148,4220,4292,4376,4427,4505,4571,4652,4735,4797,4861,4924,4993,5093,5197,5290,5390,5448,5503", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,72,86,87,79,98,118,81,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,122,87,77,53,50,65,67,73,89,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77", "endOffsets": "256,333,406,493,581,661,760,879,961,1025,1117,1185,1245,1332,1396,1458,1522,1590,1655,1709,1818,1876,1938,1992,2067,2187,2269,2349,2483,2561,2641,2764,2852,2930,2984,3035,3101,3169,3243,3333,3409,3480,3558,3628,3698,3798,3887,3965,4053,4143,4215,4287,4371,4422,4500,4566,4647,4730,4792,4856,4919,4988,5088,5192,5285,5385,5443,5498,5576"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2943,3020,3093,3180,3268,4074,4173,4292,4833,4897,4989,5221,5281,5368,5432,5494,5558,5626,5691,5745,5854,5912,5974,6028,6103,6223,6305,6385,6519,6597,6677,6800,6888,6966,7020,7071,7137,7205,7279,7369,7445,7516,7594,7664,7734,7834,7923,8001,8089,8179,8251,8323,8407,8458,8536,8602,8683,8766,8828,8892,8955,9024,9124,9228,9321,9421,9479,10053", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,120", "endColumns": "12,76,72,86,87,79,98,118,81,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,122,87,77,53,50,65,67,73,89,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77", "endOffsets": "306,3015,3088,3175,3263,3343,4168,4287,4369,4892,4984,5052,5276,5363,5427,5489,5553,5621,5686,5740,5849,5907,5969,6023,6098,6218,6300,6380,6514,6592,6672,6795,6883,6961,7015,7066,7132,7200,7274,7364,7440,7511,7589,7659,7729,7829,7918,7996,8084,8174,8246,8318,8402,8453,8531,8597,8678,8761,8823,8887,8950,9019,9119,9223,9316,9416,9474,9529,10126"}}]}]}
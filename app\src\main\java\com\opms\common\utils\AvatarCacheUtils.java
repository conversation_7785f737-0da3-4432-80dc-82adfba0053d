package com.opms.common.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.Base64;
import android.util.Log;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 用户头像缓存工具类
 * 用于处理Base64编码的图像，解码并缓存到本地
 */
public class AvatarCacheUtils {
    private static final String TAG = "AvatarCacheUtils";
    private static final String CACHE_DIR = "avatar_cache";
    private static final String AVATAR_FILE_NAME = "user_avatar.jpg";

    /**
     * 将图像文件缓存到本地
     *
     * @param context   上下文
     * @param imageFile 图像文件
     * @return 缓存的图像文件路径，如果缓存失败则返回null
     */
    public static String cacheAvatarFile(Context context, File imageFile) {
        if (context == null || imageFile == null || !imageFile.exists()) {
            Log.e(TAG, "Invalid parameters for caching avatar file");
            return null;
        }

        Log.d(TAG, "Starting to cache avatar file: " + imageFile.getAbsolutePath());

        try {
            // 创建缓存目录
            File cacheDir = new File(context.getCacheDir(), CACHE_DIR);
            Log.d(TAG, "Cache directory path: " + cacheDir.getAbsolutePath());

            if (!cacheDir.exists()) {
                Log.d(TAG, "Cache directory does not exist, creating it");
                boolean dirCreated = cacheDir.mkdirs();
                if (!dirCreated) {
                    Log.e(TAG, "Failed to create cache directory: " + cacheDir.getAbsolutePath());
                    // 尝试使用外部缓存目录
                    File externalCacheDir = context.getExternalCacheDir();
                    if (externalCacheDir != null) {
                        cacheDir = new File(externalCacheDir, CACHE_DIR);
                        Log.d(TAG, "Trying external cache directory: " + cacheDir.getAbsolutePath());
                        dirCreated = cacheDir.mkdirs();
                        if (!dirCreated && !cacheDir.exists()) {
                            Log.e(TAG, "Failed to create external cache directory");
                            return null;
                        }
                    } else {
                        Log.e(TAG, "External cache directory is null");
                        return null;
                    }
                }
            } else {
                Log.d(TAG, "Cache directory already exists");
            }

            // 复制文件到缓存目录
            File avatarFile = new File(cacheDir, AVATAR_FILE_NAME);
            Log.d(TAG, "Avatar file path: " + avatarFile.getAbsolutePath());

            try (FileInputStream fis = new FileInputStream(imageFile);
                 FileOutputStream fos = new FileOutputStream(avatarFile)) {

                byte[] buffer = new byte[1024];
                int length;
                while ((length = fis.read(buffer)) > 0) {
                    fos.write(buffer, 0, length);
                }
                fos.flush();

                Log.d(TAG, "Successfully saved avatar to file: " + avatarFile.getAbsolutePath());
                return avatarFile.getAbsolutePath();
            } catch (IOException e) {
                Log.e(TAG, "Failed to save avatar to file: " + e.getMessage());
                return null;
            }
        } catch (Exception e) {
            Log.e(TAG, "Unexpected error caching avatar: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将Base64编码的图像解码并缓存到本地
     *
     * @param context     上下文
     * @param base64Image Base64编码的图像字符串
     * @return 缓存的图像文件路径，如果解码失败则返回null
     */
    public static String cacheBase64Avatar(Context context, String base64Image) {
        if (context == null) {
            Log.e(TAG, "Context is null for caching avatar");
            return null;
        }

        if (base64Image == null || base64Image.isEmpty()) {
            Log.e(TAG, "Base64 image is null or empty");
            return null;
        }

        Log.d(TAG, "Starting to cache base64 avatar, length: " + base64Image.length());

        try {
            // 创建缓存目录
            File cacheDir = new File(context.getCacheDir(), CACHE_DIR);
            Log.d(TAG, "Cache directory path: " + cacheDir.getAbsolutePath());

            if (!cacheDir.exists()) {
                Log.d(TAG, "Cache directory does not exist, creating it");
                boolean dirCreated = cacheDir.mkdirs();
                if (!dirCreated) {
                    Log.e(TAG, "Failed to create cache directory: " + cacheDir.getAbsolutePath());
                    // 尝试使用外部缓存目录
                    File externalCacheDir = context.getExternalCacheDir();
                    if (externalCacheDir != null) {
                        cacheDir = new File(externalCacheDir, CACHE_DIR);
                        Log.d(TAG, "Trying external cache directory: " + cacheDir.getAbsolutePath());
                        dirCreated = cacheDir.mkdirs();
                        if (!dirCreated && !cacheDir.exists()) {
                            Log.e(TAG, "Failed to create external cache directory");
                            return null;
                        }
                    } else {
                        Log.e(TAG, "External cache directory is null");
                        return null;
                    }
                }
            } else {
                Log.d(TAG, "Cache directory already exists");
            }

            // 解码Base64图像
            byte[] decodedBytes;
            try {
                // 移除可能存在的Base64前缀（如 "data:image/jpeg;base64,"）
                String processedBase64 = base64Image;
                if (base64Image.contains(",")) {
                    processedBase64 = base64Image.split(",")[1];
                    Log.d(TAG, "Removed Base64 prefix");
                }

                // 尝试不同的解码选项
                try {
                    decodedBytes = Base64.decode(processedBase64, Base64.DEFAULT);
                } catch (IllegalArgumentException e) {
                    Log.w(TAG, "Failed to decode with DEFAULT, trying NO_WRAP: " + e.getMessage());
                    decodedBytes = Base64.decode(processedBase64, Base64.NO_WRAP);
                }

                Log.d(TAG, "Successfully decoded base64 image, bytes length: " + decodedBytes.length);
            } catch (IllegalArgumentException e) {
                Log.e(TAG, "Failed to decode base64 image: " + e.getMessage());
                return null;
            }

            // 将解码后的图像保存到文件
            File avatarFile = new File(cacheDir, AVATAR_FILE_NAME);
            Log.d(TAG, "Avatar file path: " + avatarFile.getAbsolutePath());

            try (FileOutputStream fos = new FileOutputStream(avatarFile)) {
                fos.write(decodedBytes);
                fos.flush();
                Log.d(TAG, "Successfully saved avatar to file: " + avatarFile.getAbsolutePath());
                return avatarFile.getAbsolutePath();
            } catch (IOException e) {
                Log.e(TAG, "Failed to save avatar to file: " + e.getMessage());
                return null;
            }
        } catch (Exception e) {
            Log.e(TAG, "Unexpected error caching avatar: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取缓存的头像文件路径
     *
     * @param context 上下文
     * @return 缓存的头像文件路径，如果文件不存在则返回null
     */
    public static String getCachedAvatarPath(Context context) {
        if (context == null) {
            Log.e(TAG, "Context is null in getCachedAvatarPath");
            return null;
        }

        // 首先检查内部缓存目录
        File cacheDir = new File(context.getCacheDir(), CACHE_DIR);
        File avatarFile = new File(cacheDir, AVATAR_FILE_NAME);

        if (avatarFile.exists() && avatarFile.length() > 0) {
            Log.d(TAG, "Found avatar in internal cache: " + avatarFile.getAbsolutePath());
            return avatarFile.getAbsolutePath();
        }

        // 如果内部缓存中没有，检查外部缓存
        File externalCacheDir = context.getExternalCacheDir();
        if (externalCacheDir != null) {
            File externalAvatarDir = new File(externalCacheDir, CACHE_DIR);
            File externalAvatarFile = new File(externalAvatarDir, AVATAR_FILE_NAME);

            if (externalAvatarFile.exists() && externalAvatarFile.length() > 0) {
                Log.d(TAG, "Found avatar in external cache: " + externalAvatarFile.getAbsolutePath());
                return externalAvatarFile.getAbsolutePath();
            }
        }

        Log.d(TAG, "No cached avatar found");
        return null;
    }

    /**
     * 检查是否有缓存的头像
     *
     * @param context 上下文
     * @return 如果有缓存的头像则返回true，否则返回false
     */
    public static boolean hasCachedAvatar(Context context) {
        String path = getCachedAvatarPath(context);
        boolean hasCache = path != null;
        Log.d(TAG, "hasCachedAvatar: " + hasCache + (hasCache ? ", path: " + path : ""));
        return hasCache;
    }

    /**
     * 创建圆形Bitmap
     */
    public static Bitmap createCircleBitmap(Bitmap bitmap) {
        if (bitmap == null) {
            return null;
        }

        try {
            int width = bitmap.getWidth();
            int height = bitmap.getHeight();
            int size = Math.min(width, height);

            Bitmap output = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888);
            android.graphics.Canvas canvas = new android.graphics.Canvas(output);

            final android.graphics.Paint paint = new android.graphics.Paint();
            final android.graphics.Rect rect = new android.graphics.Rect(0, 0, size, size);
            final android.graphics.RectF rectF = new android.graphics.RectF(rect);

            paint.setAntiAlias(true);
            canvas.drawARGB(0, 0, 0, 0);
            paint.setColor(0xFFFFFFFF);

            // 绘制圆形
            canvas.drawOval(rectF, paint);

            // 设置图像交集模式
            paint.setXfermode(new android.graphics.PorterDuffXfermode(android.graphics.PorterDuff.Mode.SRC_IN));

            // 如果原图不是正方形，需要居中裁剪
            android.graphics.Rect srcRect;
            if (width > height) {
                int left = (width - height) / 2;
                srcRect = new android.graphics.Rect(left, 0, left + height, height);
            } else if (height > width) {
                int top = (height - width) / 2;
                srcRect = new android.graphics.Rect(0, top, width, top + width);
            } else {
                srcRect = new android.graphics.Rect(0, 0, width, height);
            }

            // 绘制位图
            canvas.drawBitmap(bitmap, srcRect, rect, paint);

            Log.d(TAG, "Successfully created circle bitmap: " + size + "x" + size);
            return output;
        } catch (Exception e) {
            Log.e(TAG, "Error creating circle bitmap: " + e.getMessage(), e);
            return bitmap; // 返回原图
        }
    }

    /**
     * 清除缓存的头像
     *
     * @param context 上下文
     * @return 如果成功清除则返回true，否则返回false
     */
    public static boolean clearCachedAvatar(Context context) {
        if (context == null) {
            return false;
        }

        File cacheDir = new File(context.getCacheDir(), CACHE_DIR);
        File avatarFile = new File(cacheDir, AVATAR_FILE_NAME);

        if (avatarFile.exists()) {
            return avatarFile.delete();
        }

        return true; // 文件不存在，视为清除成功
    }
}

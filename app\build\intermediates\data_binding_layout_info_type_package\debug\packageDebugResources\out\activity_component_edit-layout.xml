<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_component_edit" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_component_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_component_edit_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="222" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="18" endOffset="66"/></Target><Target id="@+id/card_images" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="33" startOffset="12" endLine="66" endOffset="47"/></Target><Target id="@+id/rv_component_images" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="58" startOffset="20" endLine="62" endOffset="64"/></Target><Target id="@+id/card_basic_info" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="69" startOffset="12" endLine="181" endOffset="47"/></Target><Target id="@+id/et_component_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="102" startOffset="24" endLine="107" endOffset="50"/></Target><Target id="@+id/et_component_code" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="119" startOffset="24" endLine="124" endOffset="50"/></Target><Target id="@+id/et_component_model" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="136" startOffset="24" endLine="141" endOffset="50"/></Target><Target id="@+id/et_component_standard" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="153" startOffset="24" endLine="158" endOffset="50"/></Target><Target id="@+id/et_component_remark" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="169" startOffset="24" endLine="175" endOffset="50"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="193" startOffset="16" endLine="199" endOffset="39"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="201" startOffset="16" endLine="206" endOffset="50"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="215" startOffset="4" endLine="220" endOffset="35"/></Target></Targets></Layout>
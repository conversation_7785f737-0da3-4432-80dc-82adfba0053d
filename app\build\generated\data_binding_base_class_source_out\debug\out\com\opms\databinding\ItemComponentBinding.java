// Generated by view binder compiler. Do not edit!
package com.opms.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.opms.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemComponentBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialButton btnDelete;

  @NonNull
  public final MaterialButton btnEdit;

  @NonNull
  public final MaterialCardView cardComponent;

  @NonNull
  public final TextView tvComponentCode;

  @NonNull
  public final TextView tvComponentModel;

  @NonNull
  public final TextView tvComponentName;

  @NonNull
  public final TextView tvComponentRemark;

  @NonNull
  public final TextView tvComponentStandard;

  private ItemComponentBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialButton btnDelete, @NonNull MaterialButton btnEdit,
      @NonNull MaterialCardView cardComponent, @NonNull TextView tvComponentCode,
      @NonNull TextView tvComponentModel, @NonNull TextView tvComponentName,
      @NonNull TextView tvComponentRemark, @NonNull TextView tvComponentStandard) {
    this.rootView = rootView;
    this.btnDelete = btnDelete;
    this.btnEdit = btnEdit;
    this.cardComponent = cardComponent;
    this.tvComponentCode = tvComponentCode;
    this.tvComponentModel = tvComponentModel;
    this.tvComponentName = tvComponentName;
    this.tvComponentRemark = tvComponentRemark;
    this.tvComponentStandard = tvComponentStandard;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemComponentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemComponentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_component, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemComponentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_delete;
      MaterialButton btnDelete = ViewBindings.findChildViewById(rootView, id);
      if (btnDelete == null) {
        break missingId;
      }

      id = R.id.btn_edit;
      MaterialButton btnEdit = ViewBindings.findChildViewById(rootView, id);
      if (btnEdit == null) {
        break missingId;
      }

      MaterialCardView cardComponent = (MaterialCardView) rootView;

      id = R.id.tv_component_code;
      TextView tvComponentCode = ViewBindings.findChildViewById(rootView, id);
      if (tvComponentCode == null) {
        break missingId;
      }

      id = R.id.tv_component_model;
      TextView tvComponentModel = ViewBindings.findChildViewById(rootView, id);
      if (tvComponentModel == null) {
        break missingId;
      }

      id = R.id.tv_component_name;
      TextView tvComponentName = ViewBindings.findChildViewById(rootView, id);
      if (tvComponentName == null) {
        break missingId;
      }

      id = R.id.tv_component_remark;
      TextView tvComponentRemark = ViewBindings.findChildViewById(rootView, id);
      if (tvComponentRemark == null) {
        break missingId;
      }

      id = R.id.tv_component_standard;
      TextView tvComponentStandard = ViewBindings.findChildViewById(rootView, id);
      if (tvComponentStandard == null) {
        break missingId;
      }

      return new ItemComponentBinding((MaterialCardView) rootView, btnDelete, btnEdit,
          cardComponent, tvComponentCode, tvComponentModel, tvComponentName, tvComponentRemark,
          tvComponentStandard);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

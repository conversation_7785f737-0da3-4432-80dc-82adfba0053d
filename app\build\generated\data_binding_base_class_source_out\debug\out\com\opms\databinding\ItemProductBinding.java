// Generated by view binder compiler. Do not edit!
package com.opms.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.opms.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemProductBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageView btnDelete;

  @NonNull
  public final ImageView btnEdit;

  @NonNull
  public final MaterialCardView cardProduct;

  @NonNull
  public final TextView tvProductCode;

  @NonNull
  public final TextView tvProductModel;

  @NonNull
  public final TextView tvProductName;

  @NonNull
  public final TextView tvProductPrice;

  @NonNull
  public final TextView tvProductStandard;

  @NonNull
  public final TextView tvProductStatus;

  private ItemProductBinding(@NonNull MaterialCardView rootView, @NonNull ImageView btnDelete,
      @NonNull ImageView btnEdit, @NonNull MaterialCardView cardProduct,
      @NonNull TextView tvProductCode, @NonNull TextView tvProductModel,
      @NonNull TextView tvProductName, @NonNull TextView tvProductPrice,
      @NonNull TextView tvProductStandard, @NonNull TextView tvProductStatus) {
    this.rootView = rootView;
    this.btnDelete = btnDelete;
    this.btnEdit = btnEdit;
    this.cardProduct = cardProduct;
    this.tvProductCode = tvProductCode;
    this.tvProductModel = tvProductModel;
    this.tvProductName = tvProductName;
    this.tvProductPrice = tvProductPrice;
    this.tvProductStandard = tvProductStandard;
    this.tvProductStatus = tvProductStatus;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemProductBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemProductBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_product, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemProductBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_delete;
      ImageView btnDelete = ViewBindings.findChildViewById(rootView, id);
      if (btnDelete == null) {
        break missingId;
      }

      id = R.id.btn_edit;
      ImageView btnEdit = ViewBindings.findChildViewById(rootView, id);
      if (btnEdit == null) {
        break missingId;
      }

      MaterialCardView cardProduct = (MaterialCardView) rootView;

      id = R.id.tv_product_code;
      TextView tvProductCode = ViewBindings.findChildViewById(rootView, id);
      if (tvProductCode == null) {
        break missingId;
      }

      id = R.id.tv_product_model;
      TextView tvProductModel = ViewBindings.findChildViewById(rootView, id);
      if (tvProductModel == null) {
        break missingId;
      }

      id = R.id.tv_product_name;
      TextView tvProductName = ViewBindings.findChildViewById(rootView, id);
      if (tvProductName == null) {
        break missingId;
      }

      id = R.id.tv_product_price;
      TextView tvProductPrice = ViewBindings.findChildViewById(rootView, id);
      if (tvProductPrice == null) {
        break missingId;
      }

      id = R.id.tv_product_standard;
      TextView tvProductStandard = ViewBindings.findChildViewById(rootView, id);
      if (tvProductStandard == null) {
        break missingId;
      }

      id = R.id.tv_product_status;
      TextView tvProductStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvProductStatus == null) {
        break missingId;
      }

      return new ItemProductBinding((MaterialCardView) rootView, btnDelete, btnEdit, cardProduct,
          tvProductCode, tvProductModel, tvProductName, tvProductPrice, tvProductStandard,
          tvProductStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

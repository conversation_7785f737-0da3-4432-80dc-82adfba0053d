package com.opms.ui.profile;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.opms.R;
import com.opms.common.utils.AvatarCacheUtils;
import com.opms.common.utils.ImageUploadUtils;
import com.opms.common.utils.ImageUtils;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.repository.ImageUploadRepository;
import com.opms.data.repository.UserRepository;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;

/**
 * 头像查看和编辑Activity
 */
@AndroidEntryPoint
public class AvatarViewActivity extends AppCompatActivity {
    private static final String TAG = "AvatarViewActivity";
    private static final String EXTRA_EDITABLE = "editable";

    @Inject
    UserRepository userRepository;

    @Inject
    ImageUploadRepository imageUploadRepository;

    private ImageView ivAvatar;
    private final ActivityResultLauncher<Intent> imagePickerLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                    Uri selectedImage = result.getData().getData();
                    if (selectedImage != null) {
                        handleSelectedImage(selectedImage);
                    }
                }
            });
    private Button btnChooseImage;
    private Button btnCancel;
    private boolean isEditable;

    /**
     * 创建Intent用于启动此Activity
     *
     * @param context  上下文
     * @param editable 是否可编辑
     * @return Intent实例
     */
    public static Intent createIntent(Context context, boolean editable) {
        Intent intent = new Intent(context, AvatarViewActivity.class);
        intent.putExtra(EXTRA_EDITABLE, editable);
        return intent;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_avatar_view);

        isEditable = getIntent().getBooleanExtra(EXTRA_EDITABLE, false);

        initViews();
        setupListeners();
        loadAvatar();
    }

    private void initViews() {
        ivAvatar = findViewById(R.id.iv_avatar_large);
        btnChooseImage = findViewById(R.id.btn_choose_image);
        btnCancel = findViewById(R.id.btn_cancel);

        // 根据是否可编辑设置按钮可见性
        btnChooseImage.setVisibility(isEditable ? View.VISIBLE : View.GONE);
    }

    private void setupListeners() {
        btnChooseImage.setOnClickListener(v -> openImagePicker());
        btnCancel.setOnClickListener(v -> finish());
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }

    @Override
    public void finish() {
        super.finish();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }

    private void loadAvatar() {
        // 使用智能加载方法：优先从本地缓存加载，缓存不存在时从服务器获取
        ImageUtils.loadUserAvatarSmart(this, ivAvatar);
    }

    private void openImagePicker() {
        // 检查权限
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
            // 请求权限
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.READ_EXTERNAL_STORAGE},
                    1001);
        } else {
            // 已有权限，打开图片选择器
            Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
            imagePickerLauncher.launch(intent);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 1001) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // 权限获取成功，打开图片选择器
                Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
                imagePickerLauncher.launch(intent);
            } else {
                // 权限被拒绝
                Toast.makeText(this, "需要存储权限才能选择图片", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void handleSelectedImage(Uri imageUri) {
        try {
            // 显示选中的图片
            ImageUtils.loadUserAvatar(this, imageUri, ivAvatar);

            // 使用通用图片上传方法
            uploadAvatarWithGenericMethod(imageUri);
        } catch (Exception e) {
            Log.e(TAG, "Error handling selected image: " + e.getMessage());
            Toast.makeText(this, "无法加载所选图片", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 使用通用图片上传方法上传用户头像
     */
    private void uploadAvatarWithGenericMethod(Uri imageUri) {
        Log.d(TAG, "Starting avatar upload using generic method");

        // 显示加载提示
        Toast.makeText(this, "正在获取用户信息...", Toast.LENGTH_SHORT).show();

        // 异步获取当前用户ID
        getCurrentUserIdAsync(new UserIdCallback() {
            @Override
            public void onUserIdReceived(int userId) {
                Log.d(TAG, "Received user ID: " + userId + ", starting upload");

                // 获取操作人信息
                String operator = ImageUploadUtils.getCurrentUser(AvatarViewActivity.this);

                // 使用通用图片上传方法
                imageUploadRepository.uploadUserAvatar(
                        AvatarViewActivity.this,
                        imageUri,
                        userId,
                        operator,
                        new ImageUploadUtils.ImageUploadCallback() {
                            @Override
                            public void onUploadStart() {
                                Log.d(TAG, "Avatar upload started");
                                runOnUiThread(() -> {
                                    // 可以在这里显示进度条
                                    Toast.makeText(AvatarViewActivity.this, "正在上传头像...", Toast.LENGTH_SHORT).show();
                                });
                            }

                            @Override
                            public void onUploadSuccess(String imageUrl) {
                                Log.d(TAG, "Avatar upload successful: " + imageUrl);
                                runOnUiThread(() -> {
                                    // 缓存头像到本地
                                    try {
                                        Bitmap bitmap = MediaStore.Images.Media.getBitmap(getContentResolver(), imageUri);
                                        String cachedPath = cacheAvatarBitmap(AvatarViewActivity.this, bitmap);
                                        if (cachedPath != null) {
                                            Log.d(TAG, "Avatar cached successfully: " + cachedPath);
                                        }
                                    } catch (IOException e) {
                                        Log.e(TAG, "Error caching avatar: " + e.getMessage(), e);
                                    }

                                    Toast.makeText(AvatarViewActivity.this, "头像更新成功", Toast.LENGTH_SHORT).show();
                                    setResult(RESULT_OK);
                                    finish();
                                });
                            }

                            @Override
                            public void onUploadFailure(String errorMessage) {
                                Log.e(TAG, "Avatar upload failed: " + errorMessage);
                                runOnUiThread(() -> {
                                    // 即使上传失败，也尝试缓存到本地
                                    try {
                                        Bitmap bitmap = MediaStore.Images.Media.getBitmap(getContentResolver(), imageUri);
                                        String cachedPath = cacheAvatarBitmap(AvatarViewActivity.this, bitmap);
                                        if (cachedPath != null) {
                                            Log.d(TAG, "Avatar cached locally despite upload failure");
                                            Toast.makeText(AvatarViewActivity.this, "头像已保存到本地，但上传失败: " + errorMessage, Toast.LENGTH_LONG).show();
                                            setResult(RESULT_OK);
                                            finish();
                                        } else {
                                            Toast.makeText(AvatarViewActivity.this, "头像上传失败: " + errorMessage, Toast.LENGTH_LONG).show();
                                        }
                                    } catch (IOException e) {
                                        Log.e(TAG, "Error caching avatar after upload failure: " + e.getMessage(), e);
                                        Toast.makeText(AvatarViewActivity.this, "头像上传失败: " + errorMessage, Toast.LENGTH_LONG).show();
                                    }
                                });
                            }

                            @Override
                            public void onUploadComplete() {
                                Log.d(TAG, "Avatar upload completed");
                                // 上传完成的清理工作
                            }
                        }
                );
            }

            @Override
            public void onUserIdError(String errorMessage) {
                Log.e(TAG, "Failed to get user ID: " + errorMessage);
                runOnUiThread(() -> {
                    Toast.makeText(AvatarViewActivity.this, "获取用户信息失败: " + errorMessage, Toast.LENGTH_LONG).show();
                });
            }
        });
    }

    /**
     * 获取当前用户ID
     * 首先尝试从SharedPreferences获取，如果没有则从服务器获取用户信息
     */
    private void getCurrentUserIdAsync(UserIdCallback callback) {
        try {
            // 首先尝试从SharedPreferences获取
            SharedPreferences sharedPreferences = getSharedPreferences("OpmsPreferences", Context.MODE_PRIVATE);
            int cachedUserId = sharedPreferences.getInt("userId", 0);

            if (cachedUserId > 0) {
                Log.d(TAG, "Found cached user ID: " + cachedUserId);
                callback.onUserIdReceived(cachedUserId);
                return;
            }

            // 如果没有缓存的用户ID，从服务器获取
            Log.d(TAG, "No cached user ID found, fetching from server");
            userRepository.getUserProfile().enqueue(new retrofit2.Callback<ApiResponse<com.opms.data.model.response.UserResponse>>() {
                @Override
                public void onResponse(retrofit2.Call<ApiResponse<com.opms.data.model.response.UserResponse>> call,
                                       retrofit2.Response<ApiResponse<com.opms.data.model.response.UserResponse>> response) {
                    if (response.isSuccessful() && response.body() != null) {
                        ApiResponse<com.opms.data.model.response.UserResponse> apiResponse = response.body();
                        if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                            int userId = apiResponse.getData().getId();
                            Log.d(TAG, "Fetched user ID from server: " + userId);

                            // 缓存用户ID到SharedPreferences
                            sharedPreferences.edit().putInt("userId", userId).apply();

                            callback.onUserIdReceived(userId);
                        } else {
                            Log.e(TAG, "Failed to get user profile: " + apiResponse.getMessage());
                            callback.onUserIdError("获取用户信息失败: " + apiResponse.getMessage());
                        }
                    } else {
                        Log.e(TAG, "Server response not successful: " + response.code());
                        callback.onUserIdError("服务器响应失败: " + response.code());
                    }
                }

                @Override
                public void onFailure(retrofit2.Call<ApiResponse<com.opms.data.model.response.UserResponse>> call, Throwable t) {
                    Log.e(TAG, "Network error getting user profile: " + t.getMessage(), t);
                    callback.onUserIdError("网络错误: " + t.getMessage());
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error getting current user ID: " + e.getMessage(), e);
            callback.onUserIdError("获取用户ID失败: " + e.getMessage());
        }
    }

    /**
     * 缓存Bitmap到本地文件
     */
    private String cacheAvatarBitmap(Context context, Bitmap bitmap) {
        try {
            // 创建临时文件
            File cacheDir = new File(context.getCacheDir(), "avatars");
            if (!cacheDir.exists()) {
                boolean created = cacheDir.mkdirs();
                Log.d(TAG, "Created cache directory: " + created);
            }

            String fileName = "avatar_temp_" + System.currentTimeMillis() + ".jpg";
            File tempFile = new File(cacheDir, fileName);

            // 将Bitmap保存到文件
            FileOutputStream fos = new FileOutputStream(tempFile);
            boolean compressed = bitmap.compress(Bitmap.CompressFormat.JPEG, 80, fos);
            fos.flush();
            fos.close();

            if (compressed && tempFile.exists()) {
                // 使用AvatarCacheUtils缓存文件
                return AvatarCacheUtils.cacheAvatarFile(context, tempFile);
            } else {
                Log.e(TAG, "Failed to compress bitmap or create temp file");
                return null;
            }
        } catch (IOException e) {
            Log.e(TAG, "Error caching avatar bitmap: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 用户ID回调接口
     */
    private interface UserIdCallback {
        void onUserIdReceived(int userId);

        void onUserIdError(String errorMessage);
    }
}

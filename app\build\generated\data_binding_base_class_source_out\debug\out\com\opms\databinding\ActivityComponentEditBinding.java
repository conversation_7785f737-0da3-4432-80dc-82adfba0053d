// Generated by view binder compiler. Do not edit!
package com.opms.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.opms.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityComponentEditBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton btnCancel;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final CardView cardBasicInfo;

  @NonNull
  public final CardView cardImages;

  @NonNull
  public final TextInputEditText etComponentCode;

  @NonNull
  public final TextInputEditText etComponentModel;

  @NonNull
  public final TextInputEditText etComponentName;

  @NonNull
  public final TextInputEditText etComponentRemark;

  @NonNull
  public final TextInputEditText etComponentStandard;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView rvComponentImages;

  @NonNull
  public final Toolbar toolbar;

  private ActivityComponentEditBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton btnCancel, @NonNull MaterialButton btnSave,
      @NonNull CardView cardBasicInfo, @NonNull CardView cardImages,
      @NonNull TextInputEditText etComponentCode, @NonNull TextInputEditText etComponentModel,
      @NonNull TextInputEditText etComponentName, @NonNull TextInputEditText etComponentRemark,
      @NonNull TextInputEditText etComponentStandard, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView rvComponentImages, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnSave = btnSave;
    this.cardBasicInfo = cardBasicInfo;
    this.cardImages = cardImages;
    this.etComponentCode = etComponentCode;
    this.etComponentModel = etComponentModel;
    this.etComponentName = etComponentName;
    this.etComponentRemark = etComponentRemark;
    this.etComponentStandard = etComponentStandard;
    this.progressBar = progressBar;
    this.rvComponentImages = rvComponentImages;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityComponentEditBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityComponentEditBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_component_edit, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityComponentEditBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel;
      MaterialButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_save;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.card_basic_info;
      CardView cardBasicInfo = ViewBindings.findChildViewById(rootView, id);
      if (cardBasicInfo == null) {
        break missingId;
      }

      id = R.id.card_images;
      CardView cardImages = ViewBindings.findChildViewById(rootView, id);
      if (cardImages == null) {
        break missingId;
      }

      id = R.id.et_component_code;
      TextInputEditText etComponentCode = ViewBindings.findChildViewById(rootView, id);
      if (etComponentCode == null) {
        break missingId;
      }

      id = R.id.et_component_model;
      TextInputEditText etComponentModel = ViewBindings.findChildViewById(rootView, id);
      if (etComponentModel == null) {
        break missingId;
      }

      id = R.id.et_component_name;
      TextInputEditText etComponentName = ViewBindings.findChildViewById(rootView, id);
      if (etComponentName == null) {
        break missingId;
      }

      id = R.id.et_component_remark;
      TextInputEditText etComponentRemark = ViewBindings.findChildViewById(rootView, id);
      if (etComponentRemark == null) {
        break missingId;
      }

      id = R.id.et_component_standard;
      TextInputEditText etComponentStandard = ViewBindings.findChildViewById(rootView, id);
      if (etComponentStandard == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.rv_component_images;
      RecyclerView rvComponentImages = ViewBindings.findChildViewById(rootView, id);
      if (rvComponentImages == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityComponentEditBinding((CoordinatorLayout) rootView, btnCancel, btnSave,
          cardBasicInfo, cardImages, etComponentCode, etComponentModel, etComponentName,
          etComponentRemark, etComponentStandard, progressBar, rvComponentImages, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
